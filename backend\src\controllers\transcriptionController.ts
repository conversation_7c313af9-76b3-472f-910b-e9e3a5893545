import { Request, Response } from "express";
import { TranscriptionService } from "../services/transcriptionService";
import { logger } from "../config/logger";
import { asyncHandler } from "../middleware/errorHandler";
import {
  TranscriptionRequest,
  AzureTranscriptionRequest,
  ApiResponse,
  PaginatedResponse,
} from "../types";

const transcriptionService = new TranscriptionService();

/**
 * POST /api/transcription
 * Create a new transcription using mock service
 */
export const createTranscription = asyncHandler(
  async (req: Request, res: Response) => {
    const { audioUrl }: TranscriptionRequest = req.body;

    logger.info(`Creating transcription for: ${audioUrl}`);

    const transcription = await transcriptionService.createTranscription(
      audioUrl
    );

    const response: ApiResponse<{ _id: string }> = {
      success: true,
      data: { _id: transcription._id?.toString() || "" },
      message: "Transcription created successfully",
    };

    res.status(201).json(response);
  }
);

/**
 * POST /api/azure-transcription
 * Create a new transcription using Azure Speech Service
 */
export const createAzureTranscription = asyncHandler(
  async (req: Request, res: Response) => {
    const { audioUrl, language = "en-US" }: AzureTranscriptionRequest =
      req.body;

    logger.info(`Creating Azure transcription for: ${audioUrl} (${language})`);

    const transcription = await transcriptionService.createAzureTranscription(
      audioUrl,
      language
    );

    const response: ApiResponse<{ _id: string }> = {
      success: true,
      data: { _id: transcription._id?.toString() || "" },
      message: "Azure transcription created successfully",
    };

    res.status(201).json(response);
  }
);

/**
 * GET /api/transcriptions
 * Get transcriptions from the last 30 days with pagination
 */
export const getRecentTranscriptions = asyncHandler(
  async (req: Request, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    logger.info(
      `Getting recent transcriptions (page: ${page}, limit: ${limit})`
    );

    const result = await transcriptionService.getRecentTranscriptions(
      page,
      limit
    );

    const response: PaginatedResponse<any> = {
      success: true,
      data: result.transcriptions,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    };

    res.json(response);
  }
);

/**
 * GET /api/transcriptions/all
 * Get all transcriptions with pagination
 */
export const getAllTranscriptions = asyncHandler(
  async (req: Request, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    logger.info(`Getting all transcriptions (page: ${page}, limit: ${limit})`);

    const result = await transcriptionService.getAllTranscriptions(page, limit);

    const response: PaginatedResponse<any> = {
      success: true,
      data: result.transcriptions,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    };

    res.json(response);
  }
);

/**
 * GET /api/transcriptions/:id
 * Get a specific transcription by ID
 */
export const getTranscriptionById = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    logger.info(`Getting transcription by ID: ${id}`);

    const transcription = await transcriptionService.getTranscriptionById(id);

    if (!transcription) {
      res.status(404).json({
        success: false,
        error: "Transcription not found",
      });
      return;
    }

    const response: ApiResponse<any> = {
      success: true,
      data: transcription,
    };

    res.json(response);
  }
);

/**
 * GET /api/languages
 * Get supported languages for Azure Speech Service
 */
export const getSupportedLanguages = asyncHandler(
  async (_req: Request, res: Response) => {
    const languages = transcriptionService.getSupportedLanguages();

    const response: ApiResponse<string[]> = {
      success: true,
      data: languages,
      message: "Supported languages retrieved successfully",
    };

    res.json(response);
  }
);
