import Transcription, { ITranscription } from "../models/Transcription";
import { AudioService } from "./audioService";
import { AzureSpeechService } from "./azureSpeechService";
import { logger } from "../config/logger";
import { TranscriptionDocument } from "../types";

export class TranscriptionService {
  private azureSpeechService: AzureSpeechService;

  constructor() {
    this.azureSpeechService = new AzureSpeechService();
  }

  /**
   * Create a new transcription using mock service
   */
  async createTranscription(audioUrl: string): Promise<ITranscription> {
    try {
      logger.info(`Creating transcription for: ${audioUrl}`);

      // Download and transcribe audio with retry mechanism
      await AudioService.withRetry(() => AudioService.downloadAudio(audioUrl));
      const transcriptionText = await AudioService.withRetry(() =>
        AudioService.transcribeAudio(audioUrl)
      );

      // Save to database
      const transcription = new Transcription({
        audioUrl,
        transcription: transcriptionText,
        source: "mock",
        createdAt: new Date(),
      });

      const savedTranscription = await transcription.save();
      logger.info(`Transcription created with ID: ${savedTranscription._id}`);

      return savedTranscription;
    } catch (error) {
      logger.error(`Failed to create transcription for ${audioUrl}:`, error);
      throw error;
    }
  }

  /**
   * Create a new transcription using Azure Speech Service
   */
  async createAzureTranscription(
    audioUrl: string,
    language: string = "en-US"
  ): Promise<ITranscription> {
    try {
      logger.info(
        `Creating Azure transcription for: ${audioUrl} (${language})`
      );

      // Validate language
      if (!this.azureSpeechService.isLanguageSupported(language)) {
        throw new Error(`Unsupported language: ${language}`);
      }

      // Transcribe using Azure service
      const transcriptionText = await this.azureSpeechService.transcribeAudio(
        audioUrl,
        language
      );

      // Save to database
      const transcription = new Transcription({
        audioUrl,
        transcription: transcriptionText,
        source: "azure",
        language,
        createdAt: new Date(),
      });

      const savedTranscription = await transcription.save();
      logger.info(
        `Azure transcription created with ID: ${savedTranscription._id}`
      );

      return savedTranscription;
    } catch (error) {
      logger.error(
        `Failed to create Azure transcription for ${audioUrl}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get transcriptions from the last 30 days
   */
  async getRecentTranscriptions(
    page: number = 1,
    limit: number = 10
  ): Promise<{
    transcriptions: ITranscription[];
    total: number;
    totalPages: number;
  }> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const skip = (page - 1) * limit;

      const [transcriptions, total] = await Promise.all([
        Transcription.find({
          createdAt: { $gte: thirtyDaysAgo },
        })
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),

        Transcription.countDocuments({
          createdAt: { $gte: thirtyDaysAgo },
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      logger.info(
        `Retrieved ${transcriptions.length} transcriptions from last 30 days (page ${page}/${totalPages})`
      );

      return {
        transcriptions,
        total,
        totalPages,
      };
    } catch (error) {
      logger.error("Failed to get recent transcriptions:", error);
      throw error;
    }
  }

  /**
   * Get transcription by ID
   */
  async getTranscriptionById(id: string): Promise<ITranscription | null> {
    try {
      const transcription = await Transcription.findById(id);
      return transcription;
    } catch (error) {
      logger.error(`Failed to get transcription ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all transcriptions with pagination
   */
  async getAllTranscriptions(
    page: number = 1,
    limit: number = 10
  ): Promise<{
    transcriptions: ITranscription[];
    total: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * limit;

      const [transcriptions, total] = await Promise.all([
        Transcription.find().sort({ createdAt: -1 }).skip(skip).limit(limit),

        Transcription.countDocuments(),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        transcriptions,
        total,
        totalPages,
      };
    } catch (error) {
      logger.error("Failed to get all transcriptions:", error);
      throw error;
    }
  }

  /**
   * Get supported languages for Azure Speech Service
   */
  getSupportedLanguages(): string[] {
    return this.azureSpeechService.getSupportedLanguages();
  }
}
