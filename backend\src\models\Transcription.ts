import mongoose, { Schema, Document } from "mongoose";

export interface ITranscription extends Document {
  audioUrl: string;
  transcription: string;
  source: "mock" | "azure";
  language?: string;
  createdAt: Date;
  updatedAt?: Date;
}

const TranscriptionSchema: Schema = new Schema({
  audioUrl: {
    type: String,
    required: true,
    trim: true,
  },
  transcription: {
    type: String,
    required: true,
  },
  source: {
    type: String,
    enum: ["mock", "azure"],
    required: true,
    default: "mock",
  },
  language: {
    type: String,
    default: "en-US",
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Update the updatedAt field before saving
TranscriptionSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// Indexes for better query performance
TranscriptionSchema.index({ createdAt: -1 });
TranscriptionSchema.index({ source: 1, createdAt: -1 });
TranscriptionSchema.index({ audioUrl: 1 });

export default mongoose.model<ITranscription>(
  "Transcription",
  TranscriptionSchema
);
