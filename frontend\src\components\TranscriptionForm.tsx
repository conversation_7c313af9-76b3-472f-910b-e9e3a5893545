import React, { useState } from "react";
import { useMutation, useQuery } from "react-query";
// import { Button } from "./ui/button";
// import { Input } from "./ui/input";
// import { Label } from "./ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Badge } from "./ui/badge";
import { transcriptionApi } from "../services/api";
import { Loader2, Mic, MicIcon } from "lucide-react";
import { Label } from "./ui/label";
import { Input } from "./ui/input";
import { Button } from "./ui/button";

interface TranscriptionFormProps {
  onSuccess?: (id: string) => void;
}

export const TranscriptionForm: React.FC<TranscriptionFormProps> = ({
  onSuccess,
}) => {
  const [audioUrl, setAudioUrl] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("en-US");
  const [serviceType, setServiceType] = useState<"mock" | "azure">("mock");

  // Get supported languages
  const { data: languagesData } = useQuery(
    "supportedLanguages",
    transcriptionApi.getSupportedLanguages,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  // Mock transcription mutation
  const mockTranscriptionMutation = useMutation(
    transcriptionApi.createTranscription,
    {
      onSuccess: (data) => {
        if (data.success && data.data?._id) {
          onSuccess?.(data.data._id);
          setAudioUrl("");
        }
      },
    }
  );

  // Azure transcription mutation
  const azureTranscriptionMutation = useMutation(
    transcriptionApi.createAzureTranscription,
    {
      onSuccess: (data) => {
        if (data.success && data.data?._id) {
          onSuccess?.(data.data._id);
          setAudioUrl("");
        }
      },
    }
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!audioUrl.trim()) return;

    if (serviceType === "mock") {
      mockTranscriptionMutation.mutate({ audioUrl });
    } else {
      azureTranscriptionMutation.mutate({
        audioUrl,
        language: selectedLanguage,
      });
    }
  };

  const isLoading =
    mockTranscriptionMutation.isLoading || azureTranscriptionMutation.isLoading;
  const error =
    mockTranscriptionMutation.error || azureTranscriptionMutation.error;

  const sampleUrls = [
    "https://example.com/sample-audio.mp3",
    "https://example.com/test-recording.wav",
    "https://example.com/voice-memo.m4a",
    "https://example.com/podcast-clip.mp3",
  ];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mic className="h-6 w-6" />
          Create New Transcription
        </CardTitle>
        <CardDescription>
          Upload an audio file URL to generate a transcription using either our
          mock service or Azure Speech-to-Text.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Service Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="serviceType">Transcription Service</Label>
            <Select
              value={serviceType}
              onValueChange={(value: "mock" | "azure") => setServiceType(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select transcription service" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mock">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Mock</Badge>
                    <span>Mock Transcription Service</span>
                  </div>
                </SelectItem>
                <SelectItem value="azure">
                  <div className="flex items-center gap-2">
                    <Badge variant="default">Azure</Badge>
                    <span>Azure Speech-to-Text</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Language Selection (only for Azure) */}
          {serviceType === "azure" && (
            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <Select
                value={selectedLanguage}
                onValueChange={setSelectedLanguage}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {languagesData?.data?.map((lang) => (
                    <SelectItem key={lang} value={lang}>
                      {lang}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Audio URL Input */}
          <div className="space-y-2">
            <Label htmlFor="audioUrl">Audio File URL</Label>
            <Input
              id="audioUrl"
              type="url"
              placeholder="https://example.com/audio-file.mp3"
              value={audioUrl}
              onChange={(e) => setAudioUrl(e.target.value)}
              required
            />
            <div className="text-sm text-muted-foreground">
              Enter a valid URL pointing to an audio file (mp3, wav, m4a, etc.)
            </div>
          </div>

          {/* Sample URLs */}
          <div className="space-y-2">
            <Label>Sample URLs (click to use)</Label>
            <div className="flex flex-wrap gap-2">
              {sampleUrls.map((url, index) => (
                <Button
                  key={index}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setAudioUrl(url)}
                  className="text-xs"
                >
                  Sample {index + 1}
                </Button>
              ))}
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {(error as any)?.response?.data?.message ||
                (error as any)?.message ||
                "An error occurred while creating the transcription"}
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isLoading || !audioUrl.trim()}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Transcription...
              </>
            ) : (
              <>
                <MicIcon className="mr-2 h-4 w-4" />
                Create Transcription
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
